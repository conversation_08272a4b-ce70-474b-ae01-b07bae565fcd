import { describe, it, expect, vi, beforeEach } from 'vitest';
import Requester from '../../src/api/Requester.js';

/**
 * Regression test for bug: "Adding a new Employee triggers incorrectly triggers a user prompt: 'void tow tickets'"
 * 
 * The issue was that the Requester.js was showing the "void tow tickets" confirmation dialog
 * for ANY INFO_REQUIRED response, including Employee creation operations that might require
 * additional confirmation (like user conflicts).
 * 
 * The fix ensures that the "void tow tickets" dialog only appears for Driver or Employee
 * deletion operations, not for creation operations.
 */

// Mock dependencies
vi.mock('element-ui', () => ({
  MessageBox: {
    confirm: vi.fn(),
    prompt: vi.fn()
  }
}));

vi.mock('../../src/store', () => ({
  default: {
    getters: {
      __state: {
        appMode: 'TEST'
      }
    }
  }
}));

vi.mock('../../src/events/hub', () => ({
  default: {
    $emit: vi.fn()
  }
}));

describe('Employee Creation Void Tickets Bug Regression Test', () => {
  let mockContext;

  beforeEach(() => {
    mockContext = {
      commit: vi.fn(),
      rootState: {
        product: { key: 'test-product' },
        orgUnitKey: 'test-org',
        appMode: 'TEST',
        user: { Key: 'test-user' },
        instance: { Key: 'test-instance', Authentication: 'test-auth' }
      }
    };
  });

  it('should NOT trigger void tickets dialog for Employee Create operations', () => {
    const requester = new Requester(mockContext, {
      noun: 'Employee',
      verb: 'Create',
      data: {}
    });

    // This should return false, meaning the void tickets dialog should NOT be shown
    expect(requester.isDriverOrEmployeeDeletion()).toBe(false);
  });

  it('should NOT trigger void tickets dialog for Employee Update operations', () => {
    const requester = new Requester(mockContext, {
      noun: 'Employee',
      verb: 'Update',
      data: {}
    });

    // This should return false, meaning the void tickets dialog should NOT be shown
    expect(requester.isDriverOrEmployeeDeletion()).toBe(false);
  });

  it('should trigger void tickets dialog for Employee Delete operations', () => {
    const requester = new Requester(mockContext, {
      noun: 'Employee',
      verb: 'Delete',
      data: {}
    });

    // This should return true, meaning the void tickets dialog SHOULD be shown
    expect(requester.isDriverOrEmployeeDeletion()).toBe(true);
  });

  it('should trigger void tickets dialog for Driver Delete operations', () => {
    const requester = new Requester(mockContext, {
      noun: 'Driver',
      verb: 'Delete',
      data: {}
    });

    // This should return true, meaning the void tickets dialog SHOULD be shown
    expect(requester.isDriverOrEmployeeDeletion()).toBe(true);
  });

  it('should NOT trigger void tickets dialog for other noun operations', () => {
    const requester = new Requester(mockContext, {
      noun: 'Call',
      verb: 'Create',
      data: {}
    });

    // This should return false, meaning the void tickets dialog should NOT be shown
    expect(requester.isDriverOrEmployeeDeletion()).toBe(false);
  });
});
