import is from 'is_js';
import axios from 'axios';
import store from '../store';
import Hub from '../events/hub';
import { MessageBox } from 'element-ui';
import { storifyDate } from '@/utils/filters.js';
import { differenceInMilliseconds } from 'date-fns';

import {
  get,
  set,
  isNil,
  includes,
  toString,
  uniqueId,
  castArray
} from 'lodash';

import {
  VERSION,
  EVENT_ERROR,
  EVENT_LOG_OUT,
  REQUEST_LIFESPAN
} from '../config';

// @TODO
// Verify instances are destroyed after the response is received.

class Requester {
  constructor (context, options) {
    this.context = context;
    this.options = options;
    this.responseType = 'text'; // ['arraybuffer', 'blob', 'document', 'json', 'text', 'stream']
    this.returnRawResponse = get(options, 'returnRawResponse', false);
    this.shouldRemember = false;
    this.memoryKey = '';
    this.requestMiddlewareCallback = undefined;
    this.responseMiddlewareCallback = undefined;
    this.successCallback = undefined;
    this.afterSuccessCallback = undefined;
    this.failCallback = undefined;
    this.alwaysCallback = undefined;
    this.isLazy = get(options, 'isLazy', false);
    this.requestId = uniqueId();
    this.response = 'Not set';
    this.rawResponse = null;
    this.responseFormat = 'JSON';
    this.requestedAt = new Date();
    this.respondedAt = null;
    this.showGlobalNotification = get(this.options, 'showGlobalNotification', true);

    this.parameters = {
      Operation: {
        Noun: this.options.noun,
        Verb: this.options.verb,
        ProductKey: this.context.rootState.product.key,
        RequestID: this.requestId,
        OrgUnitKey: get(this.context.rootState, 'orgUnitKey'),
        Mode: get(this.context.rootState, 'appMode'),
        ResponseData: this.responseFormat,
        LastRead: storifyDate(get(this.options, 'lastRead', ''))
      },
      Authentication: {
        UserKey: this.context.rootState.user.Key,
        InstanceKey: this.context.rootState.instance.Key,
        AuthenticationKey: this.context.rootState.instance.Authentication
      },
      Data: this.options.data
    };
  }

  make () {
    if (this.hasMemory()) {
      if (store.getters.__state.appMode === 'DEBUG') {
        console.log(`%cmemory → %c${this.options.noun}, ${this.options.verb} %c(${this.requestId})`, 'font-variant: small-caps; color: #2ECC40', 'font-weight: bold', 'color: #AAAAAA');
      }

      let promise = new Promise((resolve, reject) => {
        resolve(this.getMemory());
      });

      promise.then(response => {
        if (is.truthy(this.responseMiddlewareCallback)) {
          this.responseMiddlewareCallback(response);
        }

        if (is.truthy(this.successCallback)) {
          this.successCallback(response);
        }

        if (is.truthy(this.afterSuccessCallback)) {
          this.afterSuccessCallback(response);
        }

        if (is.truthy(this.alwaysCallback)) {
          this.alwaysCallback(response);
        }
      });

      return;
    }

    if (store.getters.__state.appMode === 'DEBUG') {
      console.log(`%crequest → %c${this.options.noun}, ${this.options.verb} %c(${this.requestId})`, 'font-variant: small-caps; color: #0074D9', 'font-weight: bold', 'color: #AAAAAA', this.parameters);
    }

    this.context.commit('SET_BUSY');

    if (is.truthy(this.requestMiddlewareCallback)) {
      this.requestMiddlewareCallback(this.parameters);
    }

    axios({
      method: get(this.options, 'method', 'post'),
      headers: { 'X-txi-api': `App:TOPS Browser,Version:${VERSION},Noun:${this.options.noun},Verb:${this.options.verb}` },
      url: import.meta.env.VITE_TXI_API,
      data: this.parameters,
      responseType: this.responseType,
      timeout: get(this.options, 'timeout', REQUEST_LIFESPAN * 1000)
    })
    .then(response => {
      this.respondedAt = new Date();

      if (this.isDataExport()) {
        // Don't bother parsing the response
      } else if (this.resultIs(response, ['SUCCESS', 'NO_CHANGE'])) {
        // Success.
      } else if (this.resultIs(response, ['OVERWRITE_REQUIRED', 'OVERWRITE_REQD'])) {
        this.confirmOverwrite();
      } else if (this.resultIs(response, 'INFO_REQUIRED') && this.isTicketsTool()) {
        this.confirmTicketsAction(response);
      } else if (this.resultIs(response, 'INFO_REQUIRED') && this.isOverpayment()) {
        this.enterTowTicket();
      } else if (this.resultIs(response, 'INFO_REQUIRED') && this.isDriverOrEmployeeDeletion()) {
        this.confirmVoidTickets();
      } else if (this.resultIs(response, 'INFO_REQUIRED')) {
        this.confirmGenericAction(response);
      } else {
        // Error and default behavior.
        throw this.returnRawResponse ? response : response.data.Error;
      }

      let pathOne = get(response, 'data.Data', null);
      let pathTwo = get(response, 'data', null);

      this.response = pathOne || pathTwo;
      this.rawResponse = response;

      /**
       * This was added later after it became clear that drilling down
       * to the data by default was not always the best option. Still,
       * we need to keep the original behavior for backwards compatibility.
       */
      const selectedResponse = this.returnRawResponse ? this.rawResponse : this.response;

      this.context.commit('SET_NOT_BUSY');

      if (store.getters.__state.appMode === 'DEBUG') {
        console.log(`%cresponse → %c${this.options.noun}, ${this.options.verb} %c(${this.requestId})`, 'font-variant: small-caps; color: #39CCCC', 'font-weight: bold', 'color: #AAAAAA', selectedResponse);
      }

      if (is.truthy(this.responseMiddlewareCallback)) {
        this.responseMiddlewareCallback(selectedResponse);
      }

      if (is.all.truthy([this.shouldRemember, this.memoryKey])) {
        window.sessionStorage.setItem(this.memoryKey, JSON.stringify(selectedResponse));
      }

      if (is.truthy(this.successCallback)) {
        this.successCallback(selectedResponse);
      }

      if (is.truthy(this.afterSuccessCallback)) {
        this.afterSuccessCallback(selectedResponse);
      }

      if (is.truthy(this.alwaysCallback)) {
        this.alwaysCallback(selectedResponse);
      }
    })
    .catch(error => {
      let errorMessage = get(error, 'Message', 'Something unexpected happened.');

      if (isNil(this.respondedAt)) {
        this.respondedAt = new Date();
      }

      this.context.commit('SET_NOT_BUSY');

      if (store.getters.__state.appMode === 'DEBUG') {
        console.log(`%cerror → %c${this.options.noun}, ${this.options.verb} %c(${this.requestId})`, 'font-variant: small-caps; color: #FF4136', 'font-weight: bold', 'color: #AAAAAA', error);
      }

      if (is.truthy(this.failCallback)) {
        this.failCallback(error);
      }

      if (is.truthy(this.alwaysCallback)) {
        this.alwaysCallback(error);
      }

      if (toString(get(error, 'ValidationError', '')) === 'true') {
        // Usually hide error alert here, but make one exception
        this.showGlobalNotification = this.parameters.Operation.Verb === 'Login';

        Hub.$emit(EVENT_LOG_OUT, errorMessage);
      }

      if (errorMessage === 'The VIN Check Digit does Not Match the Calculated Value.') {
        this.showGlobalNotification = false;
      }

      // Probably want to handle this manually
      if (this.returnRawResponse) {
        this.showGlobalNotification = false;
      }

      if (this.showGlobalNotification) {
        Hub.$emit(EVENT_ERROR, errorMessage);
      }
    })
    .then(() => {
      if (store.getters.__state.appMode === 'DEBUG') {
        console.log(`%cduration → %c${this.options.noun}, ${this.options.verb} %c(${this.requestId})`, 'font-variant: small-caps; color: #F012BE', 'font-weight: bold', 'color: #AAAAAA', differenceInMilliseconds(this.respondedAt, this.requestedAt));
      }
    });
  }

  remember () {
    this.shouldRemember = true;
    this.memoryKey = this.makeMemoryKey();
    return this;
  }

  makeMemoryKey () {
    return [this.options.noun,
      this.options.verb,
      get(this.context.rootState, 'orgUnitKey'),
      JSON.stringify(this.options.data)
    ].join('-');
  }

  hasMemory () {
    return is.all.truthy([
      this.shouldRemember,
      window.sessionStorage.getItem(this.memoryKey)
    ]);
  }

  getMemory () {
    return JSON.parse(window.sessionStorage.getItem(this.memoryKey));
  }

  requestMiddleware (fn) {
    this.requestMiddlewareCallback = fn;

    return this;
  }

  responseMiddleware (fn) {
    this.responseMiddlewareCallback = fn;

    return this;
  }

  success (fn) {
    this.successCallback = fn;

    return this;
  }

  fail (fn) {
    this.failCallback = fn;

    return this;
  }

  always (fn) {
    this.alwaysCallback = fn;

    return this;
  }

  respondAs (format = 'JSON') {
    this.responseFormat = format;

    return this;
  }

  render () {
    return {
      url: import.meta.env.VITE_TXI_API,
      parameters: this.parameters
    };
  }

  resultIs (response, desiredResults) {
    desiredResults = castArray(desiredResults);

    return is.existy(response.data.Result) && is.inArray(response.data.Result, desiredResults);
  }

  confirmOverwrite () {
    MessageBox.confirm('Some data has changed since you started viewing this record.', 'Data Conflict', {
      confirmButtonText: 'Save Anyway',
      cancelButtonText: 'Cancel',
      type: 'warning'
    }).then(() => {
      set(this.options.data, 'OverwriteUpdate', true);
      this.make();
    }).catch(() => {
      return;
    });
  }

  confirmVoidTickets () {
    MessageBox.confirm('This driver has tow tickets assigned to it which will be voided.', 'Void Tow Tickets', {
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      type: 'warning'
    }).then(() => {
      set(this.options.data, 'VoidTowTickets', true);
      this.make();
    }).catch(() => {
      return;
    });
  }

  enterTowTicket () {
    MessageBox.prompt('Enter a Tow Ticket # to create an overpayment invoice.', 'Enter Tow Ticket', {
      confirmButtonText: 'Save',
      cancelButtonText: 'Cancel'
    }).then(input => {
      set(this.options.data, 'TowTicket', input.value);
      this.make();
    }).catch(() => {
      return;
    });
  }

  confirmTicketsAction (response) {
    let message = get(response, 'data.Data.MoreInfoNeeded', 'Are you sure?');

    MessageBox.confirm(message, 'Caution', {
      confirmButtonText: 'Continue',
      cancelButtonText: 'Cancel',
      type: 'warning'
    }).then(() => {
      set(this.options.data, 'Continue', true);
      this.make();
    }).catch(() => {
      return;
    });
  }

  confirmGenericAction (response) {
    let message = get(response, 'data.Data.MoreInfoNeeded', 'Are you sure?');

    MessageBox.confirm(message, 'Confirmation Required', {
      confirmButtonText: 'Continue',
      cancelButtonText: 'Cancel',
      type: 'warning'
    }).then(() => {
      set(this.options.data, 'Continue', true);
      this.make();
    }).catch(() => {
      return;
    });
  }

  isTicketsTool () {
    return includes([
      'GetStatuses',
      'BatchChangePrefix',
      'BatchDriverAssign',
      'BatchLotAssign',
      'BatchSubterminalAssign',
      'BatchVoid',
      'BatchUnvoid'],
      this.parameters.Operation.Verb
    );
  }

  isOverpayment () {
    return this.parameters.Operation.Verb === 'CreateOverpaymentInvoice';
  }

  isDataExport () {
    return includes(['GetRecordsViewDataAsCSVFile', 'GetRecordsViewDataAsPDF'], this.parameters.Operation.Verb);
  }

  isDriverOrEmployeeDeletion () {
    return (this.parameters.Operation.Noun === 'Driver' || this.parameters.Operation.Noun === 'Employee') &&
           this.parameters.Operation.Verb === 'Delete';
  }
};

export default Requester;
