import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import LegsSection from '../Index.vue';
import { VALUE_ID } from '@/config.js';

// Mock the BaseSection component
vi.mock('../../BaseSection.vue', () => ({
  default: {
    name: 'BaseSection',
    props: {
      call: { type: Object, required: false, default: null }
    },
    methods: {
      canEditProperty: vi.fn(() => true)
    }
  }
}));

// Mock the utils
vi.mock('../utils.js', () => ({
  createLeg: vi.fn(() => ({
    dStart: null,
    dFinish: null,
    lMiles: 0,
    sLocation: '',
    sDestination: '',
    sItemDescription: '',
    sNotes: ''
  })),
  validateLeg: vi.fn(() => ({ isValid: true, errors: [] }))
}));

// Mock child components
const mockComponents = {
  'app-accordian': {
    name: 'app-accordian',
    template: '<div><slot name="controls"></slot><slot name="body"></slot></div>',
    props: ['expand-on-mount']
  },
  'app-button': {
    name: 'app-button',
    template: '<button><slot></slot></button>',
    props: ['disabled', 'title']
  },
  'app-grid-form': {
    name: 'app-grid-form',
    template: '<div><slot></slot></div>',
    props: ['context']
  },
  'app-date-time': {
    name: 'app-date-time',
    template: '<input type="datetime-local" :disabled="disabled" :value="value" @input="$emit(\'input\', $event.target.value)">',
    props: ['value', 'disabled']
  },
  'app-number': {
    name: 'app-number',
    template: '<input type="number" :disabled="disabled" :value="value" @input="$emit(\'input\', $event.target.value)">',
    props: ['value', 'disabled', 'min', 'max']
  },
  'app-text': {
    name: 'app-text',
    template: '<input type="text" :disabled="disabled" :value="value" @input="$emit(\'input\', $event.target.value)">',
    props: ['value', 'disabled']
  },
  'app-textarea': {
    name: 'app-textarea',
    template: '<textarea :disabled="disabled" :value="value" @input="$emit(\'input\', $event.target.value)"></textarea>',
    props: ['value', 'disabled', 'maxlength']
  },
  'ElapsedTime': {
    name: 'ElapsedTime',
    template: '<span>Elapsed Time</span>',
    props: ['start-time']
  }
};

describe('LegsSection.vue', () => {
  let wrapper;
  let mockCall;

  beforeEach(() => {
    mockCall = {
      lCallKey: 123,
      lCallStatusTypeKey: VALUE_ID.callStatus.dispatched,
      Legs: [
        {
          dStart: '2023-01-01T10:00:00',
          dFinish: '2023-01-01T11:00:00',
          lMiles: 10,
          sLocation: 'Start Location',
          sDestination: 'End Location',
          sItemDescription: 'Test Item',
          sNotes: 'Test Notes'
        }
      ]
    };

    wrapper = mount(LegsSection, {
      propsData: {
        call: mockCall
      },
      stubs: mockComponents,
      mocks: {
        $_: {
          get: vi.fn((obj, path, defaultValue) => {
            const keys = path.split('.');
            let result = obj;
            for (const key of keys) {
              result = result?.[key];
            }
            return result !== undefined ? result : defaultValue;
          })
        }
      }
    });
  });

  describe('Call Status Restrictions', () => {
    it('should allow editing leg times when call is in dispatch mode', () => {
      expect(wrapper.vm.isCallInDispatchMode).toBe(true);
      expect(wrapper.vm.canEditLegTimes()).toBe(true);
    });

    it('should disallow editing leg times when call is unassigned', async () => {
      await wrapper.setProps({
        call: {
          ...mockCall,
          lCallStatusTypeKey: VALUE_ID.callStatus.unassigned
        }
      });

      expect(wrapper.vm.isCallInDispatchMode).toBe(false);
      expect(wrapper.vm.canEditLegTimes()).toBe(false);
    });

    it('should allow editing leg times when call is in other dispatch-related statuses', async () => {
      const dispatchStatuses = [
        VALUE_ID.callStatus.dispatched,
        VALUE_ID.callStatus.inventory,
        VALUE_ID.callStatus.completed,
        VALUE_ID.callStatus.retowDispatch
      ];

      for (const status of dispatchStatuses) {
        await wrapper.setProps({
          call: {
            ...mockCall,
            lCallStatusTypeKey: status
          }
        });

        expect(wrapper.vm.isCallInDispatchMode).toBe(true);
        expect(wrapper.vm.canEditLegTimes()).toBe(true);
      }
    });

    it('should disable start time input when call is unassigned', async () => {
      await wrapper.setProps({
        call: {
          ...mockCall,
          lCallStatusTypeKey: VALUE_ID.callStatus.unassigned
        }
      });

      const startTimeInput = wrapper.findAllComponents({ name: 'app-date-time' }).at(0);
      expect(startTimeInput.props('disabled')).toBe(true);
    });

    it('should disable finish time input when call is unassigned', async () => {
      await wrapper.setProps({
        call: {
          ...mockCall,
          lCallStatusTypeKey: VALUE_ID.callStatus.unassigned
        }
      });

      const finishTimeInput = wrapper.findAllComponents({ name: 'app-date-time' }).at(1);
      expect(finishTimeInput.props('disabled')).toBe(true);
    });

    it('should enable start time input when call is dispatched', () => {
      const startTimeInput = wrapper.findAllComponents({ name: 'app-date-time' }).at(0);
      expect(startTimeInput.props('disabled')).toBe(false);
    });

    it('should enable finish time input when call is dispatched', () => {
      const finishTimeInput = wrapper.findAllComponents({ name: 'app-date-time' }).at(1);
      expect(finishTimeInput.props('disabled')).toBe(false);
    });

    it('should still allow editing other fields when call is unassigned', async () => {
      await wrapper.setProps({
        call: {
          ...mockCall,
          lCallStatusTypeKey: VALUE_ID.callStatus.unassigned
        }
      });

      // Miles field should still be editable (uses canEditProperty)
      const milesInput = wrapper.findComponent({ name: 'app-number' });
      expect(milesInput.props('disabled')).toBe(false);

      // Location field should still be editable (uses canEditProperty)
      const locationInput = wrapper.findAllComponents({ name: 'app-text' }).at(0);
      expect(locationInput.props('disabled')).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing call status gracefully', async () => {
      await wrapper.setProps({
        call: {
          ...mockCall,
          lCallStatusTypeKey: undefined
        }
      });

      // Should default to allowing editing when status is undefined
      expect(wrapper.vm.isCallInDispatchMode).toBe(true);
      expect(wrapper.vm.canEditLegTimes()).toBe(true);
    });

    it('should handle null call status gracefully', async () => {
      await wrapper.setProps({
        call: {
          ...mockCall,
          lCallStatusTypeKey: null
        }
      });

      // Should default to allowing editing when status is null
      expect(wrapper.vm.isCallInDispatchMode).toBe(true);
      expect(wrapper.vm.canEditLegTimes()).toBe(true);
    });

    it('should respect canEditProperty restrictions even when in dispatch mode', () => {
      // Mock canEditProperty to return false
      wrapper.vm.canEditProperty = vi.fn(() => false);

      expect(wrapper.vm.isCallInDispatchMode).toBe(true);
      expect(wrapper.vm.canEditLegTimes()).toBe(false);
    });
  });
});
