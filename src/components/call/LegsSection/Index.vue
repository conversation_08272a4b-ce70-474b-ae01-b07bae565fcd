<template>
  <span id="legs-section">
    <app-accordian class="_legs" v-for="(leg, index) in legsProxy" :expand-on-mount="shouldExpandOnMount(index, leg.isJustAdded)" :key="'leg-' + index">
      <div class="_thumbnail">
        <div class="route-line" :data-in-progress="leg.dStart && !leg.dFinish"></div>
        <div class="location">
          <i class="far fa-circle-small"></i> {{ leg.sLocation || '--' }}
        </div>
        <div class="destination">
          <i class="far fa-square-small"></i> {{ leg.sDestination || '--' }}
        </div>
        <div class="status is-small" data-variant="finished" v-if="leg.dFinish">
          <i class="far fa-check"></i> {{ leg.lMiles }}mi in {{ differenceInHoursAndMinutes(leg) }}
        </div>
        <div class="status is-small" data-variant="in-progress" v-else-if="leg.dStart">
          <i class="far fa-timer"></i> <ElapsedTime :start-time="leg.dStart" />
        </div>
        <div class="status is-small" data-variant="pending" v-else>
          <i class="far fa-pause"></i> Pending
        </div>
      </div>

      <template slot="controls">
        <app-button @click="deleteLeg(index)" :disabled="!canEditProperty()" title="Delete Leg">
          <i class="far fa-trash-alt"></i>
        </app-button>
        <app-button @click="addLeg" :disabled="!canEditProperty()" title="Add Leg">
          <i class="far fa-plus"></i>
        </app-button>
      </template>

      <template slot="body">
        <app-grid-form class="_editor" context="inline">
          <div class="columns is-multiline">
            <div class="column is-4 is-left">
              <app-date-time v-model="leg.dStart" :disabled="!canEditLegTimes()">
                Start Time
              </app-date-time>
            </div>
            <div class="column is-4">
              <app-date-time v-model="leg.dFinish" :disabled="!canEditLegTimes()">
                Finish Time
              </app-date-time>
            </div>
            <div class="column is-4">
              <app-number v-model="leg.lMiles" :disabled="!canEditProperty()" :min="0" :max="9999">
                Miles
              </app-number>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="leg.sLocation" :disabled="!canEditProperty()">
                Location
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="leg.sDestination" :disabled="!canEditProperty()">
                Destination
              </app-text>
            </div>
            <div class="column is-12 is-left">
              <app-text v-model="leg.sItemDescription" :maxlength="100" :disabled="!canEditProperty()">
                Description
              </app-text>
            </div>
            <div class="column is-12 is-left is-bottom">
              <app-textarea v-model="leg.sNotes" :maxlength="255" :disabled="!canEditProperty()">
                Notes
              </app-textarea>
            </div>
          </div>
        </app-grid-form>
      </template>
    </app-accordian>
  </span>
</template>

<script>
import BaseSection from '../BaseSection.vue';
import { CALL_SECTION_LEGS, VALUE_ID } from '@/config.js';
import { createLeg, validateLeg } from './utils.js';
import ElapsedTime from './ElapsedTime.vue';

export default {
  name: 'legs-section',

  extends: BaseSection,

  components: {
    ElapsedTime
  },

  data () {
    return {
      sectionName: CALL_SECTION_LEGS,
      errors: []
    };
  },

  computed: {
    legsProxy: {
      get () {
        return this.$_.get(this.call, 'Legs', []);
      },
      set (value) {
        this.$emit('update:call', { ...this.call, Legs: value });
      }
    },

    isCallInDispatchMode () {
      const callStatus = this.$_.get(this.call, 'lCallStatusTypeKey', '');
      return callStatus !== VALUE_ID.callStatus.unassigned;
    }
  },

  methods: {
    deleteLeg (index) {
      this.call.Legs.splice(index, 1);
    },

    addLeg () {
      this.createIfMissing('Legs', []);
      const newLeg = createLeg();
      this.call.Legs.push(newLeg);
    },

    differenceInHoursAndMinutes (leg) {
      const start = new Date(leg.dStart);
      const end = new Date(leg.dFinish);
      const diff = end - start;
      const hours = Math.floor(diff / 1000 / 60 / 60);
      const minutes = Math.floor((diff / 1000 / 60) % 60);
      return `${hours}h ${minutes}m`;
    },

    shouldExpandOnMount (index, isJustAdded) {
      return index === this.legsProxy.length - 1 || isJustAdded;
    },

    validateLeg (leg) {
      const result = validateLeg(leg);
      this.errors = result.errors;
      return result.isValid;
    },

    canEditLegTimes () {
      return this.canEditProperty() && this.isCallInDispatchMode;
    }
  }
};
</script>

<style scoped>
._thumbnail {
  position: relative;

  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.25rem;
  grid-template-areas:
    "location location status"
    "destination destination status";

  overflow: hidden;
  white-space: nowrap;

  i {
    display: inline-grid;
    place-items: center;

    width: 1.5rem;
    height: 1.5rem;
    color: var(--pure-gray);
  }

  .location {
    grid-area: location;
  }

  .destination {
    grid-area: destination;
  }

  .status {
    grid-area: status;

    justify-self: end;
    align-self: start;

    padding: 0 0.5rem;
    border-radius: 3rem;

    &[data-variant="pending"] {
      color: color-mix(in oklch, var(--pure-orange) 50%, black);
      background-color: color-mix(in oklch, var(--pure-orange) 10%, transparent);
      border: 1px solid color-mix(in oklch, var(--pure-orange) 20%, transparent);

      i {
        color: var(--pure-orange);
      }
    }

    &[data-variant="in-progress"] {
      color: color-mix(in oklch, var(--pure-aqua) 50%, black);
      background-color: color-mix(in oklch, var(--pure-aqua) 10%, transparent);
      border: 1px solid color-mix(in oklch, var(--pure-aqua) 20%, transparent);

      i {
        color: var(--pure-aqua);
      }
    }

    &[data-variant="finished"] {
      color: color-mix(in oklch, var(--pure-green) 60%, black);
      background-color: color-mix(in oklch, var(--pure-green) 10%, transparent);
      border: 1px solid color-mix(in oklch, var(--pure-green) 20%, transparent);

      i {
        color: var(--pure-green);
      }
    }
  }
}

.route-line {
  position: absolute;
  top: 1rem;
  left: 0.7rem;
  bottom: 1rem;
  width: 2px;

  background: linear-gradient(180deg,
    var(--pure-gray) 50%,
    transparent 50%
  );
  background-size: 6px 6px;
  opacity: 0.5;

  &[data-in-progress="true"] {
    animation: march-vertical 1.0s linear infinite;
  }
}

@keyframes march-vertical {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 0 6px;
  }
}
</style>
